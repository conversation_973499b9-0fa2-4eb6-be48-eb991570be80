from car_rental.models.Ride import Ride
from car_rental.config import RATE_PER_UNIT_DISTANCE, RADIUS

class RideService:
    def __init__(self, ride_repository, customer_repository, car_repository):
        self.ride_repository = ride_repository
        self.customer_repository = customer_repository
        self.car_repository = car_repository

    def search_cars(self, customer_email):
        customer = self.customer_repository.get(customer_email)
        if not customer:
            raise Exception("Customer not found")
        x1, y1 = customer.location
        available_cars = []
        for car in self.car_repository.values():
            x2, y2 = car.location
            distance = abs(x1 - x2) + abs(y1 - y2)
            if distance <= RADIUS and car.is_available:
                available_cars.append(car)
        return available_cars
    
    def book_ride(self, customer_email, car_license_plate, start_location, end_location):
        customer = self.customer_repository.get(customer_email)
        if not customer:
            raise Exception("Customer not found")
        car = self.car_repository.get(car_license_plate)
        if not car or not car.is_available:
            raise Exception("Car not available")
        ride = Ride(customer, car, start_location, end_location)
        self.ride_repository.append(ride)
        
        car.is_available = False
        customer.active_ride = ride
        
        return ride
    
    def end_ride(self, customer_email):
        customer = self.customer_repository.get(customer_email)
        if not customer:
            raise Exception("Customer not found")
        ride = customer.active_ride
        if not ride:
            raise Exception("No active ride found")
        car = ride.car
        if not car:
            raise Exception("Car not found")    
        ride.status = "COMPLETED"
        ride.bill_amount = self.calculate_bill(ride)
        car.is_available = True
        car.earning += ride.bill_amount
        customer.active_ride = None
        return ride
    
    def calculate_bill(self, ride):
        x1, y1 = ride.start_location
        x2, y2 = ride.end_location
        distance = abs(x1 - x2) + abs(y1 - y2)
        rate_per_unit = RATE_PER_UNIT_DISTANCE
        return distance * rate_per_unit

    def get_total_earnings(self):
        total_earnings = 0
        for car in self.car_repository.values():
            total_earnings += car.earning
        return total_earnings
    
