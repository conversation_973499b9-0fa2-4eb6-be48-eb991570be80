class Ride:
    def __init__(self, customer, car, start_location, end_location):
        self.customer = customer
        self.car = car
        self.start_location = start_location
        self.end_location = end_location
        self.status = "ACTIVE"
        self.bill_amount = 0

    def __str__(self):
        return f"Ride(customer={self.customer.email}, car={self.car.license_plate}, status={self.status}, bill={self.bill_amount})"