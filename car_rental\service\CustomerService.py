from car_rental.models.Customer import Customer

class CustomerService:
    def __init__(self):
        self.customers = {}

    def add_customer(self, name, email, location):
        customer = Customer(name, email, location)
        if customer.email in self.customers:
            raise Exception("Customer already exists")
        self.customers[customer.email] = customer
        return customer
    
    def update_location(self, email, new_location):
        if email not in self.customers:
            raise Exception("Customer not found")
        customer = self.customers[email]
        customer.location = new_location
        return customer
    
    def update_name(self, email, name=None):
        if email not in self.customers:
            raise Exception("Customer not found")
        customer = self.customers[email]
        if name:
            customer.name = name
        return customer