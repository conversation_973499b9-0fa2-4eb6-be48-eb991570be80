from models.Customer import Customer

class CustomerService:
    def __init__(self):
        self.customers = {}

    def add_customer(self, name, email, location):
        customer = Customer(name, email, location)
        if customer.email in self.customers:
            raise Exception("Customer already exists")
        self.customers[customer.email] = customer
        return f"Customer with email {email} added successfully"
    
    def update_location(self, email, new_location):
        if email not in self.customers:
            raise Exception("Customer not found")
        customer = self.customers[email]
        old_location = customer.location
        customer.location = new_location
        return f"Customer {customer.name} location updated from {old_location} to {new_location}"

    def update_name(self, email, name=None):
        if email not in self.customers:
            raise Exception("Customer not found")
        customer = self.customers[email]
        if name:
            old_name = customer.name
            customer.name = name
            return f"Customer name updated from {old_name} to {name}"
        return f"No name provided for update"