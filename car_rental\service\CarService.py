from car_rental.models.Car import Car

class CarService:
    def __init__(self):
        self.cars = {} # key: license_plate, value: Car object

    def add_car(self, license_plate, location):
        if license_plate in self.cars:
            raise Exception("Car already exists")
        car = Car(license_plate, location)
        self.cars[license_plate] = car
        return car
    
    def get_car_earnings(self, license_plate):
        if license_plate not in self.cars:
            raise Exception("Car not found")
        car = self.cars[license_plate]
        return car.earning