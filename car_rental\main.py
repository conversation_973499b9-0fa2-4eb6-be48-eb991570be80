from car_rental.service.CarService import CarService
from car_rental.service.CustomerService import CustomerService
from car_rental.service.RideService import RideService

class Main:
    def __init__(self):
        self.car_service = CarService()
        self.customer_service = CustomerService()
        self.ride_service = RideService()

if __name__ == "__main__":
    main = Main()
    main.car_service.add_car("KA-01-12345", (1, 1))
    main.car_service.add_car("KA-01-12346", (2, 2))
    main.car_service.add_car("KA-01-12347", (3, 3))
    main.customer_service.add_customer("Alice", "<EMAIL>", (0, 0))
    main.customer_service.add_customer("Bob", "<EMAIL>", (0, 0))
    main.customer_service.add_customer("Charlie", "<EMAIL>", (0, 0))
    print("Car Rental System Initialized")
    available_cars = main.ride_service.search_cars("<EMAIL>")
    for car in available_cars:
        print(car.license_plate)
    ride = main.ride_service.book_ride("<EMAIL>", "KA-01-12345", (0, 0), (10, 10))
    print(ride.bill_amount)
    completed_ride = main.ride_service.end_ride("<EMAIL>")
    print(completed_ride.bill_amount)
    print(main.ride_service.get_total_earnings())
    print(main.car_service.get_car_earnings("KA-01-12345"))
    print(main.customer_service.update_name("<EMAIL>", "Alicia"))
    print(main.customer_service.update_location("<EMAIL>", (1, 1)))
    print("Updated Customer Info:")
    customer = main.customer_service.customers["<EMAIL>"]
    print(f"Name: {customer.name}, Location: {customer.location}")